//
//  ThirdPartySDKManager.h
//  H5SDK
//
//  Created by H5SDK on 2025/1/15.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

typedef void(^InitializationCompletionHandler)(BOOL success, NSError * _Nullable error);
typedef void(^AttributionCompletionHandler)(NSDictionary * _Nullable attribution);

@interface ThirdPartySDKManager : NSObject

// 单例访问
+ (instancetype)sharedInstance;
+ (instancetype)new NS_UNAVAILABLE;
- (instancetype)init NS_UNAVAILABLE;

// SDK初始化
- (void)initializeSDKsWithCompletion:(InitializationCompletionHandler)completion;
- (void)requestTrackingPermissionWithCompletion:(void(^)(BOOL granted))completion;

// Adjust相关
- (void)initializeAdjustWithCompletion:(InitializationCompletionHandler)completion;
- (void)getAdjustAttributionWithCompletion:(AttributionCompletionHandler)completion;
- (nullable NSDictionary *)getAdjustAttribution;

// Facebook相关
- (void)initializeFacebookWithCompletion:(InitializationCompletionHandler)completion;

// 事件上报
- (void)trackPurchaseEvent:(NSString *)orderNo
                    amount:(NSNumber *)amount 
                  currency:(NSString *)currency;

- (void)trackOrderEvent:(NSString *)orderNo 
                 amount:(NSNumber *)amount 
               currency:(NSString *)currency;

- (void)trackLoginEvent;
- (void)trackRegisterEvent;

// 归因数据上报
- (void)reportAttributionDataWithCompletion:(void(^)(BOOL success))completion;

// SDK状态检查
- (BOOL)isAdjustInitialized;
- (BOOL)isFacebookInitialized;
- (BOOL)isAllSDKsInitialized;

// 自定义事件跟踪
- (void)trackCustomEvent:(NSString *)eventName parameters:(nullable NSDictionary *)parameters;
- (void)trackCustomEventWithRevenue:(NSString *)eventName
                             amount:(NSNumber *)amount
                           currency:(NSString *)currency
                         parameters:(nullable NSDictionary *)parameters;

@end

NS_ASSUME_NONNULL_END

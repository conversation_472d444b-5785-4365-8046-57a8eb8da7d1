//
//  PaymentManager.m
//  H5SDK
//
//  Created by H5SDK on 2025/1/15.
//

#import "PaymentManager.h"
#import "ThirdPartySDKManager.h"
#import "GlobalDataManager.h"
#import "APIClient.h"
#import "Constants.h"

@interface PaymentManager ()

@property (nonatomic, assign) PaymentState currentState;
@property (nonatomic, strong) NSString *currentProductCode;
@property (nonatomic, strong) NSString *currentOrderNo;
@property (nonatomic, strong) NSString *currentSource;
@property (nonatomic, strong) NSString *currentEntry;
@property (nonatomic, copy, nullable) PaymentCompletionHandler completionHandler;
@property (nonatomic, strong) SKProductsRequest *productsRequest;
@property (nonatomic, strong) SKProduct *currentProduct;
@property (nonatomic, strong) ThirdPartySDKManager *thirdPartySDKManager;
@property (nonatomic, strong) GlobalDataManager *globalDataManager;
@property (nonatomic, strong) dispatch_queue_t paymentQueue;
@property (nonatomic, assign) BOOL isCancelled;

// 缓存的订单数据
@property (nonatomic, strong) NSNumber *currentPaidAmount;
@property (nonatomic, strong) NSString *currentPaidCurrency;

@end

@implementation PaymentManager

#pragma mark - 单例实现

+ (instancetype)sharedInstance {
    static dispatch_once_t onceToken;
    static PaymentManager *instance = nil;
    dispatch_once(&onceToken, ^{
        instance = [[super alloc] init];
    });
    return instance;
}

#pragma mark - 初始化

- (instancetype)init {
    if (self = [super init]) {
        self.currentState = PaymentStateIdle;
        self.thirdPartySDKManager = [ThirdPartySDKManager sharedInstance];
        self.globalDataManager = [GlobalDataManager sharedInstance];
        self.paymentQueue = dispatch_queue_create([Constants paymentQueueName].UTF8String, DISPATCH_QUEUE_SERIAL);
        self.isCancelled = NO;
        
        // 添加支付观察者
        [[SKPaymentQueue defaultQueue] addTransactionObserver:self];
    }
    return self;
}

- (void)dealloc {
    [[SKPaymentQueue defaultQueue] removeTransactionObserver:self];
}

#pragma mark - 状态属性

- (BOOL)isPaymentInProgress {
    return self.currentState != PaymentStateIdle && 
           self.currentState != PaymentStateCompleted && 
           self.currentState != PaymentStateFailed &&
           self.currentState != PaymentStateCancelled;
}

#pragma mark - 支付方法

- (void)purchaseProduct:(NSString *)productCode 
                 source:(nullable NSString *)source 
                  entry:(nullable NSString *)entry 
             completion:(PaymentCompletionHandler)completion {
    
    if (!productCode || productCode.length == 0) {
        if (completion) {
            NSError *error = [NSError errorWithDomain:@"PaymentError"
                                                 code:-1001
                                             userInfo:@{NSLocalizedDescriptionKey: @"Product code cannot be empty"}];
            completion(NO, error);
        }
        return;
    }

    if (self.isPaymentInProgress) {
        if (completion) {
            NSError *error = [NSError errorWithDomain:@"PaymentError"
                                                 code:-1002
                                             userInfo:@{NSLocalizedDescriptionKey: @"Payment is in progress"}];
            completion(NO, error);
        }
        return;
    }

    // 检查是否可以进行支付
    if (![SKPaymentQueue canMakePayments]) {
        if (completion) {
            NSError *error = [NSError errorWithDomain:@"PaymentError"
                                                 code:-1003
                                             userInfo:@{NSLocalizedDescriptionKey: @"Device does not support in-app purchases"}];
            completion(NO, error);
        }
        return;
    }
    
    self.currentProductCode = productCode;
    self.currentSource = source;
    self.currentEntry = entry;
    self.completionHandler = completion;
    self.isCancelled = NO;
    
    dispatch_async(self.paymentQueue, ^{
        [self executePaymentFlow];
    });
}

- (void)restorePurchasesWithCompletion:(PaymentCompletionHandler)completion {
    if (self.isPaymentInProgress) {
        if (completion) {
            NSError *error = [NSError errorWithDomain:@"PaymentError"
                                                 code:-1004
                                             userInfo:@{NSLocalizedDescriptionKey: @"Payment is in progress, cannot restore purchases"}];
            completion(NO, error);
        }
        return;
    }
    
    self.completionHandler = completion;
    [[SKPaymentQueue defaultQueue] restoreCompletedTransactions];
}

- (void)queryProductInfo:(NSString *)productCode 
              completion:(void(^)(SKProduct * _Nullable product, NSError * _Nullable error))completion {
    
    if (!productCode || productCode.length == 0) {
        if (completion) {
            NSError *error = [NSError errorWithDomain:@"PaymentError"
                                                 code:-1005
                                             userInfo:@{NSLocalizedDescriptionKey: @"Product code cannot be empty"}];
            completion(nil, error);
        }
        return;
    }
    
    NSSet *productIdentifiers = [NSSet setWithObject:productCode];
    SKProductsRequest *request = [[SKProductsRequest alloc] initWithProductIdentifiers:productIdentifiers];
    request.delegate = self;
    [request start];
}

#pragma mark - 支付流程

- (void)executePaymentFlow {
    if (self.isCancelled) return;
    
    // 步骤1: 查询商品
    [self transitionToState:PaymentStateQueryingProducts withUserInfo:nil];
    [self queryProduct];
}

- (void)queryProduct {
    NSSet *productIdentifiers = [NSSet setWithObject:self.currentProductCode];
    self.productsRequest = [[SKProductsRequest alloc] initWithProductIdentifiers:productIdentifiers];
    self.productsRequest.delegate = self;
    [self.productsRequest start];
}

- (void)createOrder {
    if (self.isCancelled) return;
    
    // 步骤2: 创建订单
    [self transitionToState:PaymentStateCreatingOrder withUserInfo:nil];
    
    // 调用APIClient创建订单
    [[APIClient sharedInstance] createOrderWithGoodsCode:self.currentProductCode
                                                   source:self.currentSource
                                                   entry:self.currentEntry
                                               completion:^(BOOL success, id _Nullable data, NSError * _Nullable error) {
        if (self.isCancelled) return;

        if (success && data) {
            // 从返回数据中获取订单号和缓存订单数据
            if ([data isKindOfClass:[NSDictionary class]]) {
                NSDictionary *responseDict = (NSDictionary *)data;
                self.currentOrderNo = responseDict[@"orderNo"] ?: @"";

                // 缓存订单的支付金额和货币
                if ([responseDict[@"paidAmount"] isKindOfClass:[NSNumber class]]) {
                    self.currentPaidAmount = responseDict[@"paidAmount"];
                }
                if ([responseDict[@"paidCurrency"] isKindOfClass:[NSString class]]) {
                    self.currentPaidCurrency = responseDict[@"paidCurrency"];
                }
            }
            // 上报下单事件
            NSNumber *paidAmount = self.currentPaidAmount ?: nil;
            NSString *paidCurrency = (self.currentPaidCurrency && self.currentPaidCurrency.length > 0) ?
                                    self.currentPaidCurrency : @"USD";
            if (paidAmount) {
                [self.thirdPartySDKManager trackOrderEvent:self.currentOrderNo
                                                    amount:paidAmount
                                                  currency:paidCurrency];
            }

            [self startPurchase];
        } else {
            // 处理创建订单失败
            NSLog(@"创建订单失败: %@", error.localizedDescription);
            [self handlePaymentError:error ?: [NSError errorWithDomain:@"PaymentError"
                                                                  code:-2001
                                                              userInfo:@{NSLocalizedDescriptionKey: @"Order creation failed"}]];
        }
    }];
}

- (void)startPurchase {
    if (self.isCancelled) return;
    
    // 步骤3: 发起购买
    [self transitionToState:PaymentStatePurchasing withUserInfo:nil];
    
    SKMutablePayment *payment = [SKMutablePayment paymentWithProduct:self.currentProduct];
    payment.applicationUsername = self.currentOrderNo; // 关联订单号
    
    [[SKPaymentQueue defaultQueue] addPayment:payment];
}

- (void)verifyPurchase:(SKPaymentTransaction *)transaction {
    if (self.isCancelled) return;
    
    // 步骤4: 验证购买
    [self transitionToState:PaymentStateVerifying withUserInfo:nil];
    
    // 获取收据数据
    NSURL *receiptURL = [[NSBundle mainBundle] appStoreReceiptURL];
    NSData *receiptData = [NSData dataWithContentsOfURL:receiptURL];

    if (!receiptData) {
        NSError *error = [NSError errorWithDomain:@"PaymentError"
                                             code:-1006
                                         userInfo:@{NSLocalizedDescriptionKey: @"Unable to get purchase receipt"}];
        [self handlePaymentError:error];
        return;
    }

    // 将收据数据转换为Base64字符串
    NSString *receiptString = [receiptData base64EncodedStringWithOptions:0];

    // 调用APIClient验证收据
    __weak typeof(self) weakSelf = self;
    [[APIClient sharedInstance] consumeIAPWithOrderNo:self.currentOrderNo
                                                receipt:receiptString
                                          transactionId:transaction.transactionIdentifier
                                             completion:^(BOOL success, id _Nullable data, NSError * _Nullable error) {
        __strong typeof(weakSelf) strongSelf = weakSelf;
        if (!strongSelf || strongSelf.isCancelled) return;

        if (success) {
            // 上报购买事件
            NSNumber *paidAmount = strongSelf.currentPaidAmount ?: nil;
            NSString *paidCurrency = (strongSelf.currentPaidCurrency && strongSelf.currentPaidCurrency.length > 0) ?
                                    strongSelf.currentPaidCurrency : @"USD";
            
            if (paidAmount) {
                [strongSelf.thirdPartySDKManager trackPurchaseEvent:strongSelf.currentOrderNo
                                                       amount:paidAmount
                                                     currency:paidCurrency];
            }

            [strongSelf completePayment:transaction];
        } else {
            // 处理收据验证失败
            NSLog(@"收据验证失败: %@", error.localizedDescription);
            [strongSelf handlePaymentError:error ?: [NSError errorWithDomain:@"PaymentError"
                                                                  code:-2002
                                                              userInfo:@{NSLocalizedDescriptionKey: @"Receipt verification failed"}]];
        }
    }];
}

- (void)completePayment:(SKPaymentTransaction *)transaction {
    if (self.isCancelled) return;
    
    // 步骤5: 完成支付
    [self transitionToState:PaymentStateCompleted withUserInfo:@{@"transaction": transaction}];
    
    // 完成交易
    [[SKPaymentQueue defaultQueue] finishTransaction:transaction];
    
    if (self.completionHandler) {
        self.completionHandler(YES, nil);
        self.completionHandler = nil;
    }
    
    [self resetPaymentState];
}

#pragma mark - SKProductsRequestDelegate

- (void)productsRequest:(SKProductsRequest *)request didReceiveResponse:(SKProductsResponse *)response {
    dispatch_async(self.paymentQueue, ^{
        if (self.isCancelled) return;

        if (response.products.count > 0) {
            self.currentProduct = response.products.firstObject;
            [self createOrder];
        } else {
            NSError *error = [NSError errorWithDomain:@"PaymentError"
                                                 code:-1007
                                             userInfo:@{NSLocalizedDescriptionKey: @"Product not found"}];
            [self handlePaymentError:error];
        }
    });
}

- (void)request:(SKRequest *)request didFailWithError:(NSError *)error {
    dispatch_async(self.paymentQueue, ^{
        [self handlePaymentError:error];
    });
}

#pragma mark - SKPaymentTransactionObserver

- (void)paymentQueue:(SKPaymentQueue *)queue updatedTransactions:(NSArray<SKPaymentTransaction *> *)transactions {
    for (SKPaymentTransaction *transaction in transactions) {
        switch (transaction.transactionState) {
            case SKPaymentTransactionStatePurchasing:
                // 购买中，无需处理
                break;

            case SKPaymentTransactionStatePurchased:
                [self verifyPurchase:transaction];
                break;

            case SKPaymentTransactionStateFailed:
                [self handleFailedTransaction:transaction];
                break;

            case SKPaymentTransactionStateRestored:
                [self handleRestoredTransaction:transaction];
                break;

            case SKPaymentTransactionStateDeferred:
                // 延迟支付，等待家长批准
                [self transitionToState:PaymentStatePurchasing withUserInfo:@{@"deferred": @YES}];
                break;

            default:
                break;
        }
    }
}

- (void)paymentQueue:(SKPaymentQueue *)queue restoreCompletedTransactionsFailedWithError:(NSError *)error {
    if (self.completionHandler) {
        self.completionHandler(NO, error);
        self.completionHandler = nil;
    }
}

- (void)paymentQueueRestoreCompletedTransactionsFinished:(SKPaymentQueue *)queue {
    if (self.completionHandler) {
        self.completionHandler(YES, nil);
        self.completionHandler = nil;
    }
}

#pragma mark - 交易处理

- (void)handleFailedTransaction:(SKPaymentTransaction *)transaction {
    [[SKPaymentQueue defaultQueue] finishTransaction:transaction];

    if (transaction.error.code == SKErrorPaymentCancelled) {
        [self transitionToState:PaymentStateCancelled withUserInfo:@{@"transaction": transaction}];

        if (self.completionHandler) {
            NSError *error = [NSError errorWithDomain:@"PaymentError"
                                                 code:-1008
                                             userInfo:@{NSLocalizedDescriptionKey: @"User cancelled payment"}];
            self.completionHandler(NO, error);
            self.completionHandler = nil;
        }
    } else {
        [self handlePaymentError:transaction.error];
    }

    [self resetPaymentState];
}

- (void)handleRestoredTransaction:(SKPaymentTransaction *)transaction {
    [[SKPaymentQueue defaultQueue] finishTransaction:transaction];
    // 恢复购买的处理逻辑
}

#pragma mark - 错误处理

- (void)handlePaymentError:(NSError *)error {
    [self transitionToState:PaymentStateFailed withUserInfo:@{@"error": error}];

    if (self.completionHandler) {
        self.completionHandler(NO, error);
        self.completionHandler = nil;
    }

    [self resetPaymentState];
}

#pragma mark - 状态管理

- (void)transitionToState:(PaymentState)newState withUserInfo:(NSDictionary *)userInfo {
    if (![self canTransitionToState:newState]) {
        NSLog(@"无效的支付状态转换: %@ -> %@", [self stateDescription:self.currentState], [self stateDescription:newState]);
        return;
    }

    PaymentState oldState = self.currentState;
    self.currentState = newState;

    NSLog(@"支付状态转换: %@ -> %@", [self stateDescription:oldState], [self stateDescription:newState]);

    // 通知状态变化
    dispatch_async(dispatch_get_main_queue(), ^{
        if (self.stateChangeHandler) {
            self.stateChangeHandler(newState, userInfo);
        }
    });
}

- (BOOL)canTransitionToState:(PaymentState)newState {
    switch (self.currentState) {
        case PaymentStateIdle:
            return newState == PaymentStateQueryingProducts;

        case PaymentStateQueryingProducts:
            return newState == PaymentStateCreatingOrder || newState == PaymentStateFailed;

        case PaymentStateCreatingOrder:
            return newState == PaymentStatePurchasing || newState == PaymentStateFailed;

        case PaymentStatePurchasing:
            return newState == PaymentStateVerifying ||
                   newState == PaymentStateFailed ||
                   newState == PaymentStateCancelled;

        case PaymentStateVerifying:
            return newState == PaymentStateCompleted || newState == PaymentStateFailed;

        case PaymentStateCompleted:
        case PaymentStateFailed:
        case PaymentStateCancelled:
            return newState == PaymentStateIdle; // 允许重置

        default:
            return NO;
    }
}

- (NSString *)stateDescription:(PaymentState)state {
    switch (state) {
        case PaymentStateIdle:
            return @"空闲";
        case PaymentStateQueryingProducts:
            return @"查询商品";
        case PaymentStateCreatingOrder:
            return @"创建订单";
        case PaymentStatePurchasing:
            return @"购买中";
        case PaymentStateVerifying:
            return @"验证购买";
        case PaymentStateCompleted:
            return @"支付完成";
        case PaymentStateFailed:
            return @"支付失败";
        case PaymentStateCancelled:
            return @"支付取消";
        default:
            return @"未知状态";
    }
}

- (void)cancelCurrentPayment {
    self.isCancelled = YES;
    [self transitionToState:PaymentStateCancelled withUserInfo:@{@"reason": @"User cancelled"}];
}

- (void)resetPaymentState {
    self.currentState = PaymentStateIdle;
    self.currentProductCode = nil;
    self.currentOrderNo = nil;
    self.currentSource = nil;
    self.currentEntry = nil;
    self.currentProduct = nil;
    self.isCancelled = NO;
    self.completionHandler = nil;

    // 清空缓存的订单数据
    self.currentPaidAmount = nil;
    self.currentPaidCurrency = nil;
}

@end
